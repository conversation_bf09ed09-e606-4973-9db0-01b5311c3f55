"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/assessment/page",{

/***/ "(app-pages-browser)/./src/app/assessment/page.tsx":
/*!*************************************!*\
  !*** ./src/app/assessment/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AssessmentPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_CheckCircle_Database_Download_FileText_Save_Shield_TestTube_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,CheckCircle,Database,Download,FileText,Save,Shield,TestTube,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_CheckCircle_Database_Download_FileText_Save_Shield_TestTube_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,CheckCircle,Database,Download,FileText,Save,Shield,TestTube,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_CheckCircle_Database_Download_FileText_Save_Shield_TestTube_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,CheckCircle,Database,Download,FileText,Save,Shield,TestTube,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_CheckCircle_Database_Download_FileText_Save_Shield_TestTube_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,CheckCircle,Database,Download,FileText,Save,Shield,TestTube,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_CheckCircle_Database_Download_FileText_Save_Shield_TestTube_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,CheckCircle,Database,Download,FileText,Save,Shield,TestTube,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_CheckCircle_Database_Download_FileText_Save_Shield_TestTube_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,CheckCircle,Database,Download,FileText,Save,Shield,TestTube,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_CheckCircle_Database_Download_FileText_Save_Shield_TestTube_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,CheckCircle,Database,Download,FileText,Save,Shield,TestTube,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/test-tube.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_CheckCircle_Database_Download_FileText_Save_Shield_TestTube_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,CheckCircle,Database,Download,FileText,Save,Shield,TestTube,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_CheckCircle_Database_Download_FileText_Save_Shield_TestTube_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,CheckCircle,Database,Download,FileText,Save,Shield,TestTube,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_CheckCircle_Database_Download_FileText_Save_Shield_TestTube_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,CheckCircle,Database,Download,FileText,Save,Shield,TestTube,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_CheckCircle_Database_Download_FileText_Save_Shield_TestTube_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,CheckCircle,Database,Download,FileText,Save,Shield,TestTube,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_assessment_DemographicsSection__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/assessment/DemographicsSection */ \"(app-pages-browser)/./src/components/assessment/DemographicsSection.tsx\");\n/* harmony import */ var _components_assessment_SymptomsSection__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/assessment/SymptomsSection */ \"(app-pages-browser)/./src/components/assessment/SymptomsSection.tsx\");\n/* harmony import */ var _components_assessment_RiskAssessmentSection__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/assessment/RiskAssessmentSection */ \"(app-pages-browser)/./src/components/assessment/RiskAssessmentSection.tsx\");\n/* harmony import */ var _components_assessment_MedicalHistorySection__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/assessment/MedicalHistorySection */ \"(app-pages-browser)/./src/components/assessment/MedicalHistorySection.tsx\");\n/* harmony import */ var _components_assessment_MentalStatusExamSection__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/assessment/MentalStatusExamSection */ \"(app-pages-browser)/./src/components/assessment/MentalStatusExamSection.tsx\");\n/* harmony import */ var _components_assessment_DiagnosisSection__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/assessment/DiagnosisSection */ \"(app-pages-browser)/./src/components/assessment/DiagnosisSection.tsx\");\n/* harmony import */ var _components_assessment_LaboratoryTestsSection__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/assessment/LaboratoryTestsSection */ \"(app-pages-browser)/./src/components/assessment/LaboratoryTestsSection.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Import assessment sections (we'll create these next)\n\n\n\n\n\n\n\nconst ASSESSMENT_SECTIONS = [\n    {\n        id: \"demographics\",\n        label: \"Demographics\",\n        icon: _barrel_optimize_names_ArrowLeft_BarChart3_Brain_CheckCircle_Database_Download_FileText_Save_Shield_TestTube_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        component: _components_assessment_DemographicsSection__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        id: \"symptoms\",\n        label: \"Symptoms\",\n        icon: _barrel_optimize_names_ArrowLeft_BarChart3_Brain_CheckCircle_Database_Download_FileText_Save_Shield_TestTube_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        component: _components_assessment_SymptomsSection__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        id: \"risk\",\n        label: \"Risk Assessment\",\n        icon: _barrel_optimize_names_ArrowLeft_BarChart3_Brain_CheckCircle_Database_Download_FileText_Save_Shield_TestTube_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n        component: _components_assessment_RiskAssessmentSection__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        id: \"history\",\n        label: \"Medical History\",\n        icon: _barrel_optimize_names_ArrowLeft_BarChart3_Brain_CheckCircle_Database_Download_FileText_Save_Shield_TestTube_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n        component: _components_assessment_MedicalHistorySection__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        id: \"mental-status\",\n        label: \"Mental Status\",\n        icon: _barrel_optimize_names_ArrowLeft_BarChart3_Brain_CheckCircle_Database_Download_FileText_Save_Shield_TestTube_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n        component: _components_assessment_MentalStatusExamSection__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    {\n        id: \"diagnosis\",\n        label: \"Diagnosis\",\n        icon: _barrel_optimize_names_ArrowLeft_BarChart3_Brain_CheckCircle_Database_Download_FileText_Save_Shield_TestTube_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n        component: _components_assessment_DiagnosisSection__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    },\n    {\n        id: \"laboratory-tests\",\n        label: \"Laboratory and Assessment Tests\",\n        icon: _barrel_optimize_names_ArrowLeft_BarChart3_Brain_CheckCircle_Database_Download_FileText_Save_Shield_TestTube_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n        component: _components_assessment_LaboratoryTestsSection__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    }\n];\nfunction AssessmentContent() {\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const assessmentId = searchParams.get(\"id\");\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"demographics\");\n    const [assessmentData, setAssessmentData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        demographics: {},\n        symptoms: {},\n        riskAssessment: {},\n        medicalHistory: {},\n        mentalStatusExam: {},\n        diagnosis: {},\n        laboratoryTests: {}\n    });\n    const [completedSections, setCompletedSections] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCompleting, setIsCompleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [saveError, setSaveError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentAssessmentId, setCurrentAssessmentId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(assessmentId);\n    // Calculate progress\n    const progress = completedSections.size / ASSESSMENT_SECTIONS.length * 100;\n    const handleAutoSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        setIsSaving(true);\n        try {\n            // Save to localStorage as backup\n            const dataToSave = {\n                data: assessmentData,\n                completedSections: Array.from(completedSections),\n                lastSaved: new Date().toISOString(),\n                assessmentId: currentAssessmentId\n            };\n            localStorage.setItem(\"psychiatric-assessment-data\", JSON.stringify(dataToSave));\n            // Save to database via API\n            const payload = {\n                ...assessmentData,\n                assessmentId: currentAssessmentId\n            };\n            const response = await fetch(\"/api/assessments\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(payload)\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to save to database\");\n            }\n            const result = await response.json();\n            if (!currentAssessmentId && result.assessmentId) {\n                setCurrentAssessmentId(result.assessmentId);\n            }\n            setLastSaved(new Date());\n        } catch (error) {\n            console.error(\"Error saving data:\", error);\n            setSaveError(error instanceof Error ? error.message : \"Failed to save assessment\");\n            // Still update lastSaved for localStorage backup\n            setLastSaved(new Date());\n        } finally{\n            setIsSaving(false);\n        }\n    }, [\n        assessmentData,\n        completedSections,\n        currentAssessmentId\n    ]);\n    // Auto-save functionality (debounced)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const saveTimer = setTimeout(()=>{\n            if (Object.keys(assessmentData.demographics).length > 0 || Object.keys(assessmentData.symptoms).length > 0) {\n                handleAutoSave();\n            }\n        }, 2000) // 2-second debounce\n        ;\n        return ()=>clearTimeout(saveTimer);\n    }, [\n        assessmentData,\n        handleAutoSave\n    ]);\n    // Load existing assessment or data from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadAssessment = async ()=>{\n            if (assessmentId) {\n                // Load existing assessment from API\n                try {\n                    const response = await fetch(\"/api/assessments?id=\".concat(assessmentId));\n                    if (response.ok) {\n                        var _assessment_symptoms, _assessment_symptoms1, _assessment_diagnoses_find, _assessment_diagnoses, _assessment_diagnoses_find1, _assessment_diagnoses1, _assessment_diagnoses2;\n                        const assessment = await response.json();\n                        console.log(\"Loaded assessment data:\", assessment);\n                        // Transform API data to match component structure\n                        const transformedData = {\n                            demographics: assessment.demographics || {},\n                            symptoms: {\n                                selectedSymptoms: ((_assessment_symptoms = assessment.symptoms) === null || _assessment_symptoms === void 0 ? void 0 : _assessment_symptoms.map((s)=>s.symptom.name)) || [],\n                                symptomDetails: ((_assessment_symptoms1 = assessment.symptoms) === null || _assessment_symptoms1 === void 0 ? void 0 : _assessment_symptoms1.reduce((acc, s)=>{\n                                    acc[s.symptom.name] = {\n                                        severity: s.severity,\n                                        duration: s.duration,\n                                        frequency: s.frequency,\n                                        notes: s.notes\n                                    };\n                                    return acc;\n                                }, {})) || {}\n                            },\n                            riskAssessment: assessment.riskAssessment || {},\n                            medicalHistory: assessment.medicalHistory || {},\n                            mentalStatusExam: assessment.mentalStatusExam || {},\n                            diagnosis: {\n                                primaryDiagnosis: ((_assessment_diagnoses = assessment.diagnoses) === null || _assessment_diagnoses === void 0 ? void 0 : (_assessment_diagnoses_find = _assessment_diagnoses.find((d)=>d.type === \"primary\")) === null || _assessment_diagnoses_find === void 0 ? void 0 : _assessment_diagnoses_find.diagnosis.name) || \"\",\n                                primaryDiagnosisCode: ((_assessment_diagnoses1 = assessment.diagnoses) === null || _assessment_diagnoses1 === void 0 ? void 0 : (_assessment_diagnoses_find1 = _assessment_diagnoses1.find((d)=>d.type === \"primary\")) === null || _assessment_diagnoses_find1 === void 0 ? void 0 : _assessment_diagnoses_find1.diagnosis.code) || \"\",\n                                secondaryDiagnoses: ((_assessment_diagnoses2 = assessment.diagnoses) === null || _assessment_diagnoses2 === void 0 ? void 0 : _assessment_diagnoses2.filter((d)=>d.type !== \"primary\").map((d)=>({\n                                        diagnosis: d.diagnosis.name,\n                                        code: d.diagnosis.code,\n                                        type: d.type\n                                    }))) || []\n                            },\n                            laboratoryTests: {\n                                selectedTests: {},\n                                testResults: assessment.laboratoryTests || []\n                            }\n                        };\n                        setAssessmentData(transformedData);\n                        // Calculate completed sections based on data\n                        const completed = new Set();\n                        if (Object.keys(transformedData.demographics).length > 0) completed.add(\"demographics\");\n                        if (transformedData.symptoms.selectedSymptoms.length > 0) completed.add(\"symptoms\");\n                        if (Object.keys(transformedData.riskAssessment).length > 0) completed.add(\"risk\");\n                        if (Object.keys(transformedData.medicalHistory).length > 0) completed.add(\"history\");\n                        if (Object.keys(transformedData.mentalStatusExam).length > 0) completed.add(\"mental-status\");\n                        if (transformedData.diagnosis.primaryDiagnosis) completed.add(\"diagnosis\");\n                        setCompletedSections(completed);\n                    }\n                } catch (error) {\n                    console.error(\"Error loading assessment:\", error);\n                }\n            } else {\n                // Load from localStorage for new assessments\n                const savedData = localStorage.getItem(\"psychiatric-assessment-data\");\n                if (savedData) {\n                    try {\n                        const parsed = JSON.parse(savedData);\n                        setAssessmentData((prev)=>parsed.data || prev);\n                        setCompletedSections(new Set(parsed.completedSections || []));\n                        setLastSaved(parsed.lastSaved ? new Date(parsed.lastSaved) : null);\n                        setCurrentAssessmentId(parsed.assessmentId || null);\n                    } catch (error) {\n                        console.error(\"Error loading saved data:\", error);\n                    }\n                }\n            }\n        };\n        loadAssessment();\n    }, [\n        assessmentId\n    ]);\n    const handleSectionUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((sectionId, data)=>{\n        setAssessmentData((prev)=>({\n                ...prev,\n                [sectionId]: data\n            }));\n        // Mark section as completed if it has required data\n        if (data && Object.keys(data).length > 0) {\n            setCompletedSections((prev)=>new Set(Array.from(prev).concat(sectionId)));\n        }\n    }, []);\n    // Create memoized onUpdate functions for each section to prevent infinite loops\n    const sectionUpdateHandlers = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const handlers = {};\n        ASSESSMENT_SECTIONS.forEach((section)=>{\n            handlers[section.id] = (data)=>handleSectionUpdate(section.id, data);\n        });\n        return handlers;\n    }, [\n        handleSectionUpdate\n    ]);\n    const handleExportData = async (format)=>{\n        try {\n            const response = await fetch(\"/api/export?format=\".concat(format));\n            if (!response.ok) {\n                throw new Error(\"Failed to export data\");\n            }\n            const blob = await response.blob();\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            a.download = \"psychiatric-assessments-\".concat(new Date().toISOString().split(\"T\")[0], \".\").concat(format);\n            a.click();\n            URL.revokeObjectURL(url);\n        } catch (error) {\n            console.error(\"Error exporting data:\", error);\n            // Fallback to local export\n            const exportData = {\n                ...assessmentData,\n                metadata: {\n                    exportDate: new Date().toISOString(),\n                    completedSections: Array.from(completedSections),\n                    progress: progress\n                }\n            };\n            if (format === \"json\") {\n                const blob = new Blob([\n                    JSON.stringify(exportData, null, 2)\n                ], {\n                    type: \"application/json\"\n                });\n                const url = URL.createObjectURL(blob);\n                const a = document.createElement(\"a\");\n                a.href = url;\n                a.download = \"psychiatric-assessment-\".concat(new Date().toISOString().split(\"T\")[0], \".json\");\n                a.click();\n                URL.revokeObjectURL(url);\n            }\n        }\n    };\n    const flattenObjectForCSV = function(obj) {\n        let prefix = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"\";\n        let flattened = {};\n        for(const key in obj){\n            if (obj[key] !== null && typeof obj[key] === \"object\" && !Array.isArray(obj[key])) {\n                Object.assign(flattened, flattenObjectForCSV(obj[key], prefix + key + \"_\"));\n            } else {\n                flattened[prefix + key] = obj[key];\n            }\n        }\n        return flattened;\n    };\n    const handleCompleteAssessment = async ()=>{\n        setIsCompleting(true);\n        try {\n            // Save current data with completed status\n            const payload = {\n                ...assessmentData,\n                assessmentId: currentAssessmentId,\n                status: \"completed\"\n            };\n            const response = await fetch(\"/api/assessments\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(payload)\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to complete assessment\");\n            }\n            // Clear localStorage\n            localStorage.removeItem(\"psychiatric-assessment-data\");\n            // Navigate to patients page\n            router.push(\"/patients\");\n        } catch (error) {\n            console.error(\"Error completing assessment:\", error);\n            alert(\"Failed to complete assessment. Please try again.\");\n        } finally{\n            setIsCompleting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"assessment-page-container\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"assessment-header-enhanced fade-in\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        href: \"/patients\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"button-modern-secondary\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_CheckCircle_Database_Download_FileText_Save_Shield_TestTube_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Back to Patients\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"assessment-title-enhanced mb-2\",\n                                                children: assessmentId ? \"Edit Assessment\" : \"New Assessment\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"assessment-subtitle-enhanced\",\n                                                children: assessmentId ? \"Continue or modify existing assessment\" : \"Complete all sections for comprehensive evaluation\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"autosave-indicator\",\n                                        children: isSaving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_CheckCircle_Database_Download_FileText_Save_Shield_TestTube_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Saving...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : lastSaved ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_CheckCircle_Database_Download_FileText_Save_Shield_TestTube_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Saved \",\n                                                        lastSaved.toLocaleTimeString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : null\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: handleCompleteAssessment,\n                                        disabled: isCompleting || progress < 100,\n                                        className: \"button-modern-primary\",\n                                        children: isCompleting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_CheckCircle_Database_Download_FileText_Save_Shield_TestTube_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"h-5 w-5 animate-spin mr-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Completing...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_CheckCircle_Database_Download_FileText_Save_Shield_TestTube_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Save & Complete\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>handleExportData(\"json\"),\n                                        className: \"button-modern-secondary\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_CheckCircle_Database_Download_FileText_Save_Shield_TestTube_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Export JSON\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>handleExportData(\"csv\"),\n                                        className: \"hover:bg-slate-50 border-slate-300 hover:border-slate-400 transition-all duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_CheckCircle_Database_Download_FileText_Save_Shield_TestTube_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Export CSV\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                    lineNumber: 324,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"mb-8 progress-card-enhanced slide-in-right\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            className: \"pb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-2xl font-bold text-slate-800\",\n                                        children: \"Assessment Progress\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"badge-modern badge-info\",\n                                        children: [\n                                            completedSections.size,\n                                            \" of \",\n                                            ASSESSMENT_SECTIONS.length,\n                                            \" sections completed\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                            lineNumber: 403,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_5__.Progress, {\n                                        value: progress,\n                                        className: \"w-full h-4 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-sm font-semibold text-slate-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"0%\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\",\n                                                children: [\n                                                    Math.round(progress),\n                                                    \"% Complete\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"100%\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                    lineNumber: 402,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"card-modern fade-in\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                            value: activeTab,\n                            onValueChange: setActiveTab,\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-b border-slate-200/50 bg-gradient-to-r from-slate-50 to-blue-50/30\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsList, {\n                                        className: \"grid w-full grid-cols-7 h-auto p-3 bg-transparent\",\n                                        children: ASSESSMENT_SECTIONS.map((section)=>{\n                                            const Icon = section.icon;\n                                            const isCompleted = completedSections.has(section.id);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                                value: section.id,\n                                                className: \"section-tab-enhanced flex flex-col items-center space-y-3 p-5 rounded-xl mx-1 transition-all duration-300 hover:bg-white/50 data-[state=active]:section-tab-active\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                className: \"h-6 w-6\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_CheckCircle_Database_Download_FileText_Save_Shield_TestTube_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-5 w-5 text-emerald-500 pulse-glow\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-bold leading-tight text-center\",\n                                                        children: section.label\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, section.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 13\n                                }, this),\n                                ASSESSMENT_SECTIONS.map((section)=>{\n                                    const Component = section.component;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                        value: section.id,\n                                        className: \"form-section-modern fade-in\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                                            data: assessmentData[section.id],\n                                            onUpdate: sectionUpdateHandlers[section.id]\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, section.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                            lineNumber: 428,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n                    lineNumber: 426,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n            lineNumber: 322,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n        lineNumber: 321,\n        columnNumber: 5\n    }, this);\n}\n_s(AssessmentContent, \"RPj23Pf+SMYFZ9TLqLcRGlPVjJw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AssessmentContent;\nfunction AssessmentPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: \"Loading assessment...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n            lineNumber: 472,\n            columnNumber: 25\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AssessmentContent, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n            lineNumber: 473,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\app\\\\assessment\\\\page.tsx\",\n        lineNumber: 472,\n        columnNumber: 5\n    }, this);\n}\n_c1 = AssessmentPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"AssessmentContent\");\n$RefreshReg$(_c1, \"AssessmentPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/assessment/page.tsx\n"));

/***/ })

});