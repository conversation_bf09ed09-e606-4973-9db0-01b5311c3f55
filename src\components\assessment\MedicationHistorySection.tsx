"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Plus, Trash2 } from "lucide-react"
import { MEDICATION_CATEGORIES, MEDICATION_MAPPINGS } from "@/lib/constants"

const EFFECTIVENESS_LEVELS = [
  'Very Effective',
  'Moderately Effective',
  'Minimally Effective',
  'Ineffective',
  'Unknown'
]

const DISCONTINUATION_REASONS = [
  'Ineffective',
  'Side effects',
  'Patient preference',
  'Cost',
  'Other'
]

interface MedicationEntry {
  id: string
  medicationName: string
  category: string
  dosage: string
  startDate: string // month/year format
  endDate: string // month/year format, optional for current medications
  effectiveness: string
  sideEffects: string
  discontinuationReason: string
  discontinuationOther: string
  notes: string
}

interface MedicationHistorySectionProps {
  data: MedicationEntry[]
  onUpdate: (data: MedicationEntry[]) => void
}

export default function MedicationHistorySection({ data, onUpdate }: MedicationHistorySectionProps) {
  const [medications, setMedications] = useState<MedicationEntry[]>(data || [])
  const [selectedMedication, setSelectedMedication] = useState<string>('')
  const [selectedCategory, setSelectedCategory] = useState<string>('')

  // Initialize from props data
  useEffect(() => {
    if (data && data.length !== medications.length) {
      setMedications(data)
    }
  }, [data, medications.length])

  // Call onUpdate when medications change
  useEffect(() => {
    onUpdate(medications)
  }, [medications, onUpdate])

  const addMedication = () => {
    if (!selectedMedication || !selectedCategory) return

    const newMedication: MedicationEntry = {
      id: Date.now().toString(),
      medicationName: selectedMedication,
      category: selectedCategory,
      dosage: '',
      startDate: '',
      endDate: '',
      effectiveness: '',
      sideEffects: '',
      discontinuationReason: '',
      discontinuationOther: '',
      notes: ''
    }

    setMedications(prev => [...prev, newMedication])
    setSelectedMedication('')
    setSelectedCategory('')
  }

  const removeMedication = (id: string) => {
    setMedications(prev => prev.filter(m => m.id !== id))
  }

  const updateMedication = (id: string, field: keyof MedicationEntry, value: string) => {
    setMedications(prev => prev.map(m => 
      m.id === id ? { ...m, [field]: value } : m
    ))
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Medication History</CardTitle>
        <CardDescription>
          Track psychiatric medications with effectiveness, side effects, and treatment outcomes
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Add New Medication */}
        <div className="border rounded-lg p-4 bg-slate-50">
          <h4 className="font-medium mb-3">Add Medication</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>Medication Category</Label>
              <Select value={selectedCategory} onValueChange={(value) => {
                setSelectedCategory(value)
                setSelectedMedication('') // Reset medication when category changes
              }}>
                <SelectTrigger>
                  <SelectValue placeholder="Select category first" />
                </SelectTrigger>
                <SelectContent>
                  {MEDICATION_CATEGORIES.map(category => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Specific Medication</Label>
              <Select
                value={selectedMedication}
                onValueChange={setSelectedMedication}
                disabled={!selectedCategory}
              >
                <SelectTrigger>
                  <SelectValue placeholder={selectedCategory ? "Select medication" : "Select category first"} />
                </SelectTrigger>
                <SelectContent>
                  {selectedCategory && MEDICATION_MAPPINGS[selectedCategory]?.map(med => (
                    <SelectItem key={med} value={med}>
                      {med}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end">
              <Button
                onClick={addMedication}
                disabled={!selectedMedication || !selectedCategory}
                className="w-full"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Medication
              </Button>
            </div>
          </div>
        </div>

        {/* Current Medications List */}
        {medications.length > 0 && (
          <div className="space-y-4">
            <h4 className="font-medium">Medication History</h4>
            {medications.map((medication) => (
              <Card key={medication.id} className="border-l-4 border-l-green-500">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">{medication.category}</Badge>
                      <span className="font-medium">{medication.medicationName}</span>
                      {medication.dosage && <Badge variant="secondary">{medication.dosage}</Badge>}
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeMedication(medication.id)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label>Dosage</Label>
                      <Input
                        value={medication.dosage}
                        onChange={(e) => updateMedication(medication.id, 'dosage', e.target.value)}
                        placeholder="e.g., 10mg daily"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Start Date (Month/Year)</Label>
                      <Input
                        value={medication.startDate}
                        onChange={(e) => updateMedication(medication.id, 'startDate', e.target.value)}
                        placeholder="e.g., 03/2023"
                        type="month"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>End Date (Month/Year)</Label>
                      <Input
                        value={medication.endDate}
                        onChange={(e) => updateMedication(medication.id, 'endDate', e.target.value)}
                        placeholder="Leave empty if current"
                        type="month"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Effectiveness</Label>
                      <Select 
                        value={medication.effectiveness} 
                        onValueChange={(value) => updateMedication(medication.id, 'effectiveness', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select effectiveness" />
                        </SelectTrigger>
                        <SelectContent>
                          {EFFECTIVENESS_LEVELS.map(level => (
                            <SelectItem key={level} value={level}>
                              {level}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2 md:col-span-2">
                      <Label>Reason for Discontinuation</Label>
                      <div className="flex space-x-2">
                        <Select 
                          value={medication.discontinuationReason} 
                          onValueChange={(value) => updateMedication(medication.id, 'discontinuationReason', value)}
                        >
                          <SelectTrigger className="flex-1">
                            <SelectValue placeholder="Select reason (if discontinued)" />
                          </SelectTrigger>
                          <SelectContent>
                            {DISCONTINUATION_REASONS.map(reason => (
                              <SelectItem key={reason} value={reason}>
                                {reason}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {medication.discontinuationReason === 'Other' && (
                          <Input
                            value={medication.discontinuationOther}
                            onChange={(e) => updateMedication(medication.id, 'discontinuationOther', e.target.value)}
                            placeholder="Specify other reason"
                            className="flex-1"
                          />
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Side Effects</Label>
                    <Textarea
                      value={medication.sideEffects}
                      onChange={(e) => updateMedication(medication.id, 'sideEffects', e.target.value)}
                      placeholder="Describe any side effects experienced"
                      rows={2}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Additional Notes</Label>
                    <Textarea
                      value={medication.notes}
                      onChange={(e) => updateMedication(medication.id, 'notes', e.target.value)}
                      placeholder="Additional details about medication use, response, etc."
                      rows={2}
                    />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {medications.length === 0 && (
          <div className="text-center py-8 text-slate-500">
            <p>No medication history recorded.</p>
            <p className="text-sm">Use the form above to add medication information.</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export type { MedicationEntry }
