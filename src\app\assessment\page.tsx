"use client"

import { useState, useEffect, use<PERSON><PERSON>back, useMemo, Suspense } from "react"
import { useSearch<PERSON>ara<PERSON>, useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"
import {
  Brain,
  Users,
  Shield,
  FileText,
  BarChart3,
  Database,
  TestTube,
  Save,
  Download,
  ArrowLeft,
  CheckCircle,
  AlertCircle
} from "lucide-react"
import Link from "next/link"

// Import assessment sections (we'll create these next)
import DemographicsSection from "@/components/assessment/DemographicsSection"
import SymptomsSection from "@/components/assessment/SymptomsSection"
import RiskAssessmentSection from "@/components/assessment/RiskAssessmentSection"
import MedicalHistorySection from "@/components/assessment/MedicalHistorySection"
import MentalStatusExamSection from "@/components/assessment/MentalStatusExamSection"
import DiagnosisSection from "@/components/assessment/DiagnosisSection"
import LaboratoryTestsSection from "@/components/assessment/LaboratoryTestsSection"

interface AssessmentData {
  demographics: any
  symptoms: any
  riskAssessment: any
  medicalHistory: any
  mentalStatusExam: any
  diagnosis: any
  laboratoryTests: any
}

const ASSESSMENT_SECTIONS = [
  { id: "demographics", label: "Demographics", icon: Users, component: DemographicsSection },
  { id: "symptoms", label: "Symptoms", icon: Brain, component: SymptomsSection },
  { id: "risk", label: "Risk Assessment", icon: Shield, component: RiskAssessmentSection },
  { id: "history", label: "Medical History", icon: FileText, component: MedicalHistorySection },
  { id: "mental-status", label: "Mental Status", icon: BarChart3, component: MentalStatusExamSection },
  { id: "diagnosis", label: "Diagnosis", icon: Database, component: DiagnosisSection },
  { id: "laboratory-tests", label: "Laboratory and Assessment Tests", icon: TestTube, component: LaboratoryTestsSection },
]

function AssessmentContent() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const assessmentId = searchParams.get('id')

  const [activeTab, setActiveTab] = useState("demographics")
  const [assessmentData, setAssessmentData] = useState<AssessmentData>({
    demographics: {},
    symptoms: {},
    riskAssessment: {},
    medicalHistory: {},
    mentalStatusExam: {},
    diagnosis: {},
    laboratoryTests: {}
  })
  const [completedSections, setCompletedSections] = useState<Set<string>>(new Set())
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [isSaving, setIsSaving] = useState(false)
  const [isCompleting, setIsCompleting] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [saveError, setSaveError] = useState<string | null>(null)
  const [currentAssessmentId, setCurrentAssessmentId] = useState<string | null>(assessmentId)

  // Calculate progress
  const progress = (completedSections.size / ASSESSMENT_SECTIONS.length) * 100

  const handleAutoSave = useCallback(async () => {
    setIsSaving(true)
    try {
      // Save to localStorage as backup
      const dataToSave = {
        data: assessmentData,
        completedSections: Array.from(completedSections),
        lastSaved: new Date().toISOString(),
        assessmentId: currentAssessmentId
      }
      localStorage.setItem('psychiatric-assessment-data', JSON.stringify(dataToSave))

      // Save to database via API
      const payload = {
        ...assessmentData,
        assessmentId: currentAssessmentId
      }

      const response = await fetch('/api/assessments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      })

      if (!response.ok) {
        throw new Error('Failed to save to database')
      }

      const result = await response.json()
      if (!currentAssessmentId && result.assessmentId) {
        setCurrentAssessmentId(result.assessmentId)
      }

      setLastSaved(new Date())
    } catch (error) {
      console.error('Error saving data:', error)
      setSaveError(error instanceof Error ? error.message : 'Failed to save assessment')
      // Still update lastSaved for localStorage backup
      setLastSaved(new Date())
    } finally {
      setIsSaving(false)
    }
  }, [assessmentData, completedSections, currentAssessmentId])

  // Auto-save functionality (debounced)
  useEffect(() => {
    const saveTimer = setTimeout(() => {
      if (Object.keys(assessmentData.demographics).length > 0 ||
          Object.keys(assessmentData.symptoms).length > 0) {
        handleAutoSave()
      }
    }, 2000) // 2-second debounce

    return () => clearTimeout(saveTimer)
  }, [assessmentData, handleAutoSave])

  // Load existing assessment or data from localStorage on mount
  useEffect(() => {
    const loadAssessment = async () => {
      if (assessmentId) {
        // Load existing assessment from API
        try {
          const response = await fetch(`/api/assessments?id=${assessmentId}`)
          if (response.ok) {
            const assessment = await response.json()
            console.log('Loaded assessment data:', assessment)

            // Transform API data to match component structure
            const transformedData = {
              demographics: assessment.demographics || {},
              symptoms: {
                selectedSymptoms: assessment.symptoms?.map((s: any) => s.symptom.name) || [],
                symptomDetails: assessment.symptoms?.reduce((acc: any, s: any) => {
                  acc[s.symptom.name] = {
                    severity: s.severity,
                    duration: s.duration,
                    frequency: s.frequency,
                    notes: s.notes
                  }
                  return acc
                }, {}) || {}
              },
              riskAssessment: assessment.riskAssessment || {},
              medicalHistory: assessment.medicalHistory || {},
              mentalStatusExam: assessment.mentalStatusExam || {},
              diagnosis: {
                primaryDiagnosis: assessment.diagnoses?.find((d: any) => d.type === 'primary')?.diagnosis.name || '',
                primaryDiagnosisCode: assessment.diagnoses?.find((d: any) => d.type === 'primary')?.diagnosis.code || '',
                secondaryDiagnoses: assessment.diagnoses?.filter((d: any) => d.type !== 'primary').map((d: any) => ({
                  diagnosis: d.diagnosis.name,
                  code: d.diagnosis.code,
                  type: d.type
                })) || []
              },
              laboratoryTests: {
                selectedTests: {},
                testResults: assessment.laboratoryTests || []
              }
            }

            setAssessmentData(transformedData)

            // Calculate completed sections based on data
            const completed = new Set<string>()
            if (Object.keys(transformedData.demographics).length > 0) completed.add('demographics')
            if (transformedData.symptoms.selectedSymptoms.length > 0) completed.add('symptoms')
            if (Object.keys(transformedData.riskAssessment).length > 0) completed.add('risk')
            if (Object.keys(transformedData.medicalHistory).length > 0) completed.add('history')
            if (Object.keys(transformedData.mentalStatusExam).length > 0) completed.add('mental-status')
            if (transformedData.diagnosis.primaryDiagnosis) completed.add('diagnosis')

            setCompletedSections(completed)
          }
        } catch (error) {
          console.error('Error loading assessment:', error)
        }
      } else {
        // Load from localStorage for new assessments
        const savedData = localStorage.getItem('psychiatric-assessment-data')
        if (savedData) {
          try {
            const parsed = JSON.parse(savedData)
            setAssessmentData(prev => parsed.data || prev)
            setCompletedSections(new Set(parsed.completedSections || []))
            setLastSaved(parsed.lastSaved ? new Date(parsed.lastSaved) : null)
            setCurrentAssessmentId(parsed.assessmentId || null)
          } catch (error) {
            console.error('Error loading saved data:', error)
          }
        }
      }
    }

    loadAssessment()
  }, [assessmentId])

  const handleSectionUpdate = useCallback((sectionId: string, data: any) => {
    setAssessmentData(prev => ({
      ...prev,
      [sectionId]: data
    }))

    // Mark section as completed if it has required data
    if (data && Object.keys(data).length > 0) {
      setCompletedSections(prev => new Set(Array.from(prev).concat(sectionId)))
    }
  }, [])

  // Create memoized onUpdate functions for each section to prevent infinite loops
  const sectionUpdateHandlers = useMemo(() => {
    const handlers: Record<string, (data: any) => void> = {}
    ASSESSMENT_SECTIONS.forEach(section => {
      handlers[section.id] = (data: any) => handleSectionUpdate(section.id, data)
    })
    return handlers
  }, [handleSectionUpdate])

  const handleExportData = async (format: 'csv' | 'json') => {
    try {
      const response = await fetch(`/api/export?format=${format}`)

      if (!response.ok) {
        throw new Error('Failed to export data')
      }

      const blob = await response.blob()
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `psychiatric-assessments-${new Date().toISOString().split('T')[0]}.${format}`
      a.click()
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Error exporting data:', error)
      // Fallback to local export
      const exportData = {
        ...assessmentData,
        metadata: {
          exportDate: new Date().toISOString(),
          completedSections: Array.from(completedSections),
          progress: progress
        }
      }

      if (format === 'json') {
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `psychiatric-assessment-${new Date().toISOString().split('T')[0]}.json`
        a.click()
        URL.revokeObjectURL(url)
      }
    }
  }

  const flattenObjectForCSV = (obj: any, prefix = ''): any => {
    let flattened: any = {}
    for (const key in obj) {
      if (obj[key] !== null && typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
        Object.assign(flattened, flattenObjectForCSV(obj[key], prefix + key + '_'))
      } else {
        flattened[prefix + key] = obj[key]
      }
    }
    return flattened
  }

  const handleCompleteAssessment = async () => {
    setIsCompleting(true)
    try {
      // Save current data with completed status
      const payload = {
        ...assessmentData,
        assessmentId: currentAssessmentId,
        status: 'completed'
      }

      const response = await fetch('/api/assessments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      })

      if (!response.ok) {
        throw new Error('Failed to complete assessment')
      }

      // Clear localStorage
      localStorage.removeItem('psychiatric-assessment-data')

      // Navigate to patients page
      router.push('/patients')
    } catch (error) {
      console.error('Error completing assessment:', error)
      alert('Failed to complete assessment. Please try again.')
    } finally {
      setIsCompleting(false)
    }
  }

  return (
    <div className="assessment-page-container">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Enhanced Header */}
        <div className="assessment-header-enhanced fade-in">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-8">
              <Link href="/patients">
                <Button variant="outline" size="sm" className="button-modern-secondary">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Patients
                </Button>
              </Link>
              <div>
                <h1 className="assessment-title-enhanced mb-2">
                  {assessmentId ? 'Edit Assessment' : 'New Assessment'}
                </h1>
                <p className="assessment-subtitle-enhanced">
                  {assessmentId ? 'Continue or modify existing assessment' : 'Complete all sections for comprehensive evaluation'}
                </p>
              </div>
            </div>

          <div className="flex items-center space-x-4">
            {/* Enhanced Auto-save indicator */}
            <div className="autosave-indicator">
              {isSaving ? (
                <>
                  <Save className="h-4 w-4 animate-spin" />
                  <span>Saving...</span>
                </>
              ) : saveError ? (
                <>
                  <AlertCircle className="h-4 w-4 text-red-500" />
                  <span className="text-red-600">{saveError}</span>
                </>
              ) : lastSaved ? (
                <>
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>Saved {lastSaved.toLocaleTimeString()}</span>
                </>
              ) : null}
            </div>

            {/* Enhanced Save & Complete button */}
            <Button
              onClick={handleCompleteAssessment}
              disabled={isCompleting || progress < 100}
              className="button-modern-primary"
            >
              {isCompleting ? (
                <>
                  <Save className="h-5 w-5 animate-spin mr-3" />
                  Completing...
                </>
              ) : (
                <>
                  <CheckCircle className="h-5 w-5 mr-3" />
                  Save & Complete
                </>
              )}
            </Button>

            {/* Enhanced Export buttons */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExportData('json')}
              className="button-modern-secondary"
            >
              <Download className="h-4 w-4 mr-2" />
              Export JSON
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExportData('csv')}
              className="hover:bg-slate-50 border-slate-300 hover:border-slate-400 transition-all duration-200"
            >
              <Download className="h-4 w-4 mr-2" />
              Export CSV
            </Button>
          </div>
        </div>
      </div>

        {/* Enhanced Progress indicator */}
        <Card className="mb-8 progress-card-enhanced slide-in-right">
          <CardHeader className="pb-6">
            <div className="flex items-center justify-between">
              <CardTitle className="text-2xl font-bold text-slate-800">Assessment Progress</CardTitle>
              <span className="badge-modern badge-info">
                {completedSections.size} of {ASSESSMENT_SECTIONS.length} sections completed
              </span>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Progress value={progress} className="w-full h-4 mb-4" />
              <div className="flex justify-between text-sm font-semibold text-slate-600">
                <span>0%</span>
                <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                  {Math.round(progress)}% Complete
                </span>
                <span>100%</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Enhanced Main assessment interface */}
        <Card className="card-modern fade-in">
          <CardContent className="p-0">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <div className="border-b border-slate-200/50 bg-gradient-to-r from-slate-50 to-blue-50/30">
              <TabsList className="grid w-full grid-cols-7 h-auto p-3 bg-transparent">
                {ASSESSMENT_SECTIONS.map((section) => {
                  const Icon = section.icon
                  const isCompleted = completedSections.has(section.id)
                  return (
                    <TabsTrigger
                      key={section.id}
                      value={section.id}
                      className="section-tab-enhanced flex flex-col items-center space-y-3 p-5 rounded-xl mx-1 transition-all duration-300 hover:bg-white/50 data-[state=active]:section-tab-active"
                    >
                      <div className="flex items-center space-x-2">
                        <Icon className="h-6 w-6" />
                        {isCompleted && <CheckCircle className="h-5 w-5 text-emerald-500 pulse-glow" />}
                      </div>
                      <span className="text-sm font-bold leading-tight text-center">{section.label}</span>
                    </TabsTrigger>
                  )
                })}
              </TabsList>
            </div>

            {ASSESSMENT_SECTIONS.map((section) => {
              const Component = section.component
              return (
                <TabsContent key={section.id} value={section.id} className="form-section-modern fade-in">
                  <Component
                    data={assessmentData[section.id as keyof AssessmentData]}
                    onUpdate={sectionUpdateHandlers[section.id]}
                  />
                </TabsContent>
              )
            })}
          </Tabs>
        </CardContent>
      </Card>
      </div>
    </div>
  )
}

export default function AssessmentPage() {
  return (
    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading assessment...</div>}>
      <AssessmentContent />
    </Suspense>
  )
}
