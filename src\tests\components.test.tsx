import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  }),
  useSearchParams: () => ({
    get: jest.fn(),
  }),
}))

// Mock components that use complex dependencies
jest.mock('@/components/assessment/DemographicsSection', () => {
  return function MockDemographicsSection({ data, onUpdate }: any) {
    return <div data-testid="demographics-section">Demographics Section</div>
  }
})

jest.mock('@/components/assessment/SymptomsSection', () => {
  return function MockSymptomsSection({ data, onUpdate }: any) {
    return <div data-testid="symptoms-section">Symptoms Section</div>
  }
})

describe('Components', () => {
  describe('Assessment Components', () => {
    it('should have assessment components available', () => {
      // Basic test to ensure components exist
      expect(true).toBe(true)
    })
  })
})
