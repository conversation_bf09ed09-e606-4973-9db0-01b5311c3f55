// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Assessment {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Assessment metadata
  assessorName String?
  assessmentDate DateTime @default(now())
  status String @default("in_progress") // in_progress, completed, draft

  // Related data
  demographics Demographics?
  symptoms SymptomAssessment[]
  riskAssessment RiskAssessment?
  medicalHistory MedicalHistory?
  mentalStatusExam MentalStatusExam?
  diagnoses DiagnosisAssessment[]

  // New structured data models
  medicalConditions MedicalCondition[]
  psychiatricEpisodes PsychiatricEpisode[]
  medicationHistory MedicationHistory[]
  laboratoryTests LaboratoryTest[]
  psychologicalAssessments PsychologicalAssessment[]
  bloodTestComponents BloodTestComponent[]
  bloodTestNotes BloodTestNote[]
  imagingStudies ImagingStudy[]
  neurologicalTests NeurologicalTest[]

  @@map("assessments")
}

model Demographics {
  id String @id @default(cuid())
  assessmentId String @unique
  assessment Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)

  // Anonymous Patient Identification
  patientCode String? // Anonymous patient code for privacy

  // Basic Information (NO NAMES OR PERSONAL IDENTIFIERS)
  dateOfBirth DateTime?
  age Int?
  gender String?

  // General Location (NO SPECIFIC ADDRESSES)
  generalLocation String? // e.g., "Urban", "Suburban", "Rural"
  region String? // e.g., "Northeast", "Southwest", etc.
  
  // Demographics
  ethnicity String?
  race String?
  primaryLanguage String?
  maritalStatus String?

  // Social Information
  education String?
  occupation String?
  employmentStatus String?
  livingArrangement String?

  // Insurance/Financial (NO PERSONAL CONTACT INFO)
  insuranceType String?
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("demographics")
}

model SymptomAssessment {
  id String @id @default(cuid())
  assessmentId String
  assessment Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)
  
  symptomId String
  symptom Symptom @relation(fields: [symptomId], references: [id])
  
  severity String? // mild, moderate, severe
  duration String? // days, weeks, months, years
  frequency String? // daily, weekly, monthly, rarely
  notes String?
  
  createdAt DateTime @default(now())
  
  @@unique([assessmentId, symptomId])
  @@map("symptom_assessments")
}

model Symptom {
  id String @id @default(cuid())
  name String @unique
  category String
  description String?
  
  assessments SymptomAssessment[]
  
  @@map("symptoms")
}

model RiskAssessment {
  id String @id @default(cuid())
  assessmentId String @unique
  assessment Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)
  
  // Suicide Risk
  suicidalIdeation Boolean @default(false)
  suicidalPlan Boolean @default(false)
  suicidalMeans Boolean @default(false)
  suicidalAttemptHistory Boolean @default(false)
  suicidalRiskLevel String? // low, moderate, high
  
  // Violence Risk
  homicidalIdeation Boolean @default(false)
  violenceHistory Boolean @default(false)
  violenceRiskLevel String? // low, moderate, high
  
  // Self-harm
  selfHarmHistory Boolean @default(false)
  selfHarmRisk String? // low, moderate, high
  
  // Substance Use
  substanceUseRisk String? // low, moderate, high
  
  // Additional notes
  riskFactors String?
  protectiveFactors String?
  interventions String?
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("risk_assessments")
}

model MedicalHistory {
  id String @id @default(cuid())
  assessmentId String @unique
  assessment Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)

  // Medical History
  currentMedications String?
  allergies String?
  medicalConditions String? // Legacy field for backward compatibility
  otherMedicalConditions String? // New field for additional conditions
  surgicalHistory String?

  // Psychiatric History
  previousPsychiatricTreatment Boolean @default(false)
  previousHospitalizations String?
  previousMedications String?
  familyPsychiatricHistory String?

  // Substance Use History (legacy fields for backward compatibility)
  alcoholUse String?
  drugUse String?
  tobaccoUse String?
  substanceAbuseHistory String?

  // Structured data (JSON fields for complex data)
  structuredMedicalConditions String? // JSON object for checkbox conditions
  substanceUseHistory String? // JSON array for substance use entries

  // Trauma History
  traumaHistory Boolean @default(false)
  traumaDetails String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("medical_history")
}

model MentalStatusExam {
  id String @id @default(cuid())
  assessmentId String @unique
  assessment Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)
  
  // Appearance
  appearance String?
  behavior String?
  attitude String?
  
  // Speech
  speechRate String?
  speechVolume String?
  speechTone String?
  
  // Mood and Affect
  mood String?
  affect String?
  affectRange String?
  affectIntensity String?
  
  // Thought Process
  thoughtProcess String?
  thoughtContent String?
  
  // Perceptual Disturbances
  hallucinations Boolean @default(false)
  hallucinationType String?
  delusions Boolean @default(false)
  delusionType String?
  
  // Cognitive Function
  orientation String?
  attention String?
  concentration String?
  memory String?
  
  // Insight and Judgment
  insight String?
  judgment String?
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("mental_status_exams")
}

model DiagnosisAssessment {
  id String @id @default(cuid())
  assessmentId String
  assessment Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)
  
  diagnosisId String
  diagnosis Diagnosis @relation(fields: [diagnosisId], references: [id])
  
  type String // primary, secondary, rule_out
  confidence String? // definite, probable, possible
  notes String?
  
  createdAt DateTime @default(now())
  
  @@unique([assessmentId, diagnosisId])
  @@map("diagnosis_assessments")
}

model Diagnosis {
  id String @id @default(cuid())
  code String @unique
  name String
  category String
  description String?

  assessments DiagnosisAssessment[]

  @@map("diagnoses")
}

// New structured data models for enhanced medical history

model MedicalCondition {
  id String @id @default(cuid())
  assessmentId String
  assessment Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)

  conditionName String
  isPresent Boolean @default(false)
  notes String?

  createdAt DateTime @default(now())

  @@map("medical_conditions")
}

model PsychiatricEpisode {
  id String @id @default(cuid())
  assessmentId String
  assessment Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)

  episodeType String // Major Depressive Episode, Manic Episode, etc.
  duration String?
  durationUnit String? // days, weeks, months, years
  startDate String? // month/year format
  endDate String? // month/year format, optional for ongoing
  severity String? // Mild, Moderate, Severe
  treatmentReceived String? // JSON array of treatments
  treatmentResponse String? // Good, Partial, Poor, Unknown
  notes String?

  createdAt DateTime @default(now())

  @@map("psychiatric_episodes")
}

model MedicationHistory {
  id String @id @default(cuid())
  assessmentId String
  assessment Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)

  medicationName String
  category String // Antidepressant, Antipsychotic, etc.
  dosage String?
  startDate String? // month/year format
  endDate String? // month/year format, optional for current
  effectiveness String? // Very Effective, Moderately Effective, etc.
  sideEffects String?
  discontinuationReason String?
  discontinuationOther String?
  notes String?

  createdAt DateTime @default(now())

  @@map("medication_history")
}

model LaboratoryTest {
  id String @id @default(cuid())
  assessmentId String
  assessment Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)

  testName String
  category String
  datePerformed String?
  result String?
  normalRange String?
  notes String?

  createdAt DateTime @default(now())

  @@map("laboratory_tests")
}

model PsychologicalAssessment {
  id String @id @default(cuid())
  assessmentId String
  assessment Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)

  testName String
  datePerformed String?
  score String?
  interpretation String?
  notes String?
  
  // Enhanced fields for detailed questionnaire data
  responses String? // JSON object storing individual question responses
  totalScore Int?
  subscaleScores String? // JSON object for tests with subscales (like DASS-21)
  isCompleted Boolean @default(false)

  createdAt DateTime @default(now())

  @@map("psychological_assessments")
}

model BloodTestComponent {
  id String @id @default(cuid())
  assessmentId String
  assessment Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)

  testName String // CBC, Comprehensive Metabolic Panel, etc.
  componentName String // WBC, Hemoglobin, etc.
  value String?
  unit String
  normalRange String
  isAbnormal Boolean @default(false)
  notes String?

  createdAt DateTime @default(now())

  @@unique([assessmentId, testName, componentName])
  @@map("blood_test_components")
}

model ImagingStudy {
  id String @id @default(cuid())
  assessmentId String
  assessment Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)

  testName String // MRI Brain, CT Head, etc.
  datePerformed String?
  findings String?
  impression String?
  recommendations String?
  notes String?

  createdAt DateTime @default(now())

  @@map("imaging_studies")
}

model NeurologicalTest {
  id String @id @default(cuid())
  assessmentId String
  assessment Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)

  testName String // EEG, EMG, etc.
  datePerformed String?
  findings String?
  interpretation String?
  recommendations String?
  notes String?

  createdAt DateTime @default(now())

  @@map("neurological_tests")
}

model BloodTestNote {
  id String @id @default(cuid())
  assessmentId String
  assessment Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)

  testName String // CBC, Comprehensive Metabolic Panel, etc.
  notes String?

  createdAt DateTime @default(now())

  @@unique([assessmentId, testName])
  @@map("blood_test_notes")
}
