"use client"

import { useState, useEffect } from "react"
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Plus } from "lucide-react"

// Enhanced hierarchical test structure with conditional displays
const TEST_CATEGORIES = {
  "Blood Tests": [
    "CBC (Complete Blood Count)",
    "Comprehensive Metabolic Panel",
    "Lipid Panel",
    "TSH (Thyroid Stimulating Hormone)",
    "T3 (Triiodothyronine)",
    "T4 (Thyroxine)",
    "Vitamin B12",
    "Vitamin D",
    "Folate"
  ],
  "Imaging Studies": [
    "MRI Brain",
    "CT Head",
    "PET Scan",
    "X-Ray Chest"
  ],
  "Psychological Assessments": [
    "MMSE (Mini-Mental State Exam)",
    "PHQ-9 (Patient Health Questionnaire)",
    "GAD-7 (Generalized Anxiety Disorder Scale)",
    "AUDIT (Alcohol Use Disorders Test)",
    "Beck Depression Inventory"
  ],
  "Neurological Tests": [
    "EEG (Electroencephalogram)",
    "EMG (Electromyography)",
    "Nerve Conduction Studies"
  ]
}

// Blood test component values for all tests
const BLOOD_TEST_COMPONENTS = {
  "CBC (Complete Blood Count)": [
    { name: "WBC", label: "White Blood Cells", unit: "K/μL", normalRange: "4.5-11.0" },
    { name: "Hemoglobin", label: "Hemoglobin", unit: "g/dL", normalRange: "12.0-16.0" },
    { name: "Platelets", label: "Platelets", unit: "K/μL", normalRange: "150-450" },
    { name: "Neutrophils", label: "Neutrophils", unit: "%", normalRange: "50-70" },
    { name: "Lymphocytes", label: "Lymphocytes", unit: "%", normalRange: "20-40" },
    { name: "Monocytes", label: "Monocytes", unit: "%", normalRange: "2-8" },
    { name: "Eosinophils", label: "Eosinophils", unit: "%", normalRange: "1-4" },
    { name: "Basophils", label: "Basophils", unit: "%", normalRange: "0-2" }
  ],
  "Comprehensive Metabolic Panel": [
    { name: "Glucose", label: "Glucose", unit: "mg/dL", normalRange: "70-100" },
    { name: "BUN", label: "Blood Urea Nitrogen", unit: "mg/dL", normalRange: "7-20" },
    { name: "Creatinine", label: "Creatinine", unit: "mg/dL", normalRange: "0.6-1.2" },
    { name: "Sodium", label: "Sodium", unit: "mEq/L", normalRange: "136-145" },
    { name: "Potassium", label: "Potassium", unit: "mEq/L", normalRange: "3.5-5.0" },
    { name: "Chloride", label: "Chloride", unit: "mEq/L", normalRange: "98-107" },
    { name: "CO2", label: "Carbon Dioxide", unit: "mEq/L", normalRange: "22-29" },
    { name: "Calcium", label: "Calcium", unit: "mg/dL", normalRange: "8.5-10.5" }
  ],
  "Lipid Panel": [
    { name: "TotalCholesterol", label: "Total Cholesterol", unit: "mg/dL", normalRange: "<200" },
    { name: "LDL", label: "LDL Cholesterol", unit: "mg/dL", normalRange: "<100" },
    { name: "HDL", label: "HDL Cholesterol", unit: "mg/dL", normalRange: ">40 (M), >50 (F)" },
    { name: "Triglycerides", label: "Triglycerides", unit: "mg/dL", normalRange: "<150" }
  ],
  "TSH (Thyroid Stimulating Hormone)": [
    { name: "TSH", label: "TSH", unit: "mIU/L", normalRange: "0.4-4.0" }
  ],
  "T3 (Triiodothyronine)": [
    { name: "T3", label: "T3", unit: "ng/dL", normalRange: "80-200" }
  ],
  "T4 (Thyroxine)": [
    { name: "T4", label: "T4", unit: "μg/dL", normalRange: "4.5-12.0" }
  ],
  "Vitamin B12": [
    { name: "B12", label: "Vitamin B12", unit: "pg/mL", normalRange: "200-900" }
  ],
  "Vitamin D": [
    { name: "VitaminD", label: "25-Hydroxy Vitamin D", unit: "ng/mL", normalRange: "30-100" }
  ],
  "Folate": [
    { name: "Folate", label: "Folate", unit: "ng/mL", normalRange: "2.7-17.0" }
  ]
}

// Psychological assessment questionnaires
const PSYCHOLOGICAL_QUESTIONNAIRES = {
  "PHQ-9 (Patient Health Questionnaire)": {
    questions: [
      "Little interest or pleasure in doing things",
      "Feeling down, depressed, or hopeless",
      "Trouble falling or staying asleep, or sleeping too much",
      "Feeling tired or having little energy",
      "Poor appetite or overeating",
      "Feeling bad about yourself or that you are a failure",
      "Trouble concentrating on things",
      "Moving or speaking slowly or being fidgety/restless",
      "Thoughts that you would be better off dead or hurting yourself"
    ],
    scoring: "0-4: Minimal depression, 5-9: Mild depression, 10-14: Moderate depression, 15-19: Moderately severe depression, 20-27: Severe depression"
  },
  "GAD-7 (Generalized Anxiety Disorder Scale)": {
    questions: [
      "Feeling nervous, anxious, or on edge",
      "Not being able to stop or control worrying",
      "Worrying too much about different things",
      "Trouble relaxing",
      "Being so restless that it's hard to sit still",
      "Becoming easily annoyed or irritable",
      "Feeling afraid as if something awful might happen"
    ],
    scoring: "0-4: Minimal anxiety, 5-9: Mild anxiety, 10-14: Moderate anxiety, 15-21: Severe anxiety"
  },
  "MMSE (Mini-Mental State Exam)": {
    questions: [
      "What year is it? (1 point)",
      "What season is it? (1 point)",
      "What date is it? (1 point)",
      "What day of the week is it? (1 point)",
      "What month is it? (1 point)",
      "What state are we in? (1 point)",
      "What county are we in? (1 point)",
      "What town/city are we in? (1 point)",
      "What building are we in? (1 point)",
      "What floor are we on? (1 point)",
      "Name three objects (apple, penny, table) - Registration (3 points)",
      "Serial 7s: 100-7, 93-7, 86-7, 79-7, 72-7 (5 points)",
      "Recall the three objects named earlier (3 points)",
      "Name a pencil (1 point)",
      "Name a watch (1 point)",
      "Repeat: 'No ifs, ands, or buts' (1 point)",
      "Follow 3-stage command: Take paper, fold it, put it on floor (3 points)",
      "Read and obey: 'Close your eyes' (1 point)",
      "Write a sentence (1 point)",
      "Copy intersecting pentagons (1 point)"
    ],
    scoring: "24-30: Normal cognition, 18-23: Mild cognitive impairment, 0-17: Severe cognitive impairment",
    maxScore: 30,
    type: "mmse"
  },
  "DASS-21 (Depression, Anxiety and Stress Scale)": {
    questions: [
      "I found it hard to wind down (Stress)",
      "I was aware of dryness of my mouth (Anxiety)",
      "I couldn't seem to experience any positive feeling at all (Depression)",
      "I experienced breathing difficulty (Anxiety)",
      "I found it difficult to work up the initiative to do things (Depression)",
      "I tended to over-react to situations (Stress)",
      "I experienced trembling (eg, in the hands) (Anxiety)",
      "I felt that I was using a lot of nervous energy (Stress)",
      "I was worried about situations in which I might panic (Anxiety)",
      "I felt that I had nothing to look forward to (Depression)",
      "I found myself getting agitated (Stress)",
      "I found it difficult to relax (Stress)",
      "I felt down-hearted and blue (Depression)",
      "I was intolerant of anything that kept me from getting on with what I was doing (Stress)",
      "I felt I was close to panic (Anxiety)",
      "I was unable to become enthusiastic about anything (Depression)",
      "I felt I wasn't worth much as a person (Depression)",
      "I felt that I was rather touchy (Stress)",
      "I was aware of the action of my heart in the absence of physical exertion (Anxiety)",
      "I felt scared without any good reason (Anxiety)",
      "I felt that life was meaningless (Depression)"
    ],
    scoring: "Depression: 0-9 Normal, 10-13 Mild, 14-20 Moderate, 21-27 Severe, 28+ Extremely Severe | Anxiety: 0-7 Normal, 8-9 Mild, 10-14 Moderate, 15-19 Severe, 20+ Extremely Severe | Stress: 0-14 Normal, 15-18 Mild, 19-25 Moderate, 26-33 Severe, 34+ Extremely Severe",
    subscales: {
      depression: [2, 4, 9, 12, 15, 16, 20],
      anxiety: [1, 3, 6, 8, 14, 18, 19],
      stress: [0, 5, 7, 10, 11, 13, 17]
    },
    type: "dass21"
  }
}



interface BloodTestComponent {
  name: string
  value: string
  unit: string
  normalRange: string
  notes?: string
}

interface PsychologicalAssessmentResult {
  testName: string
  responses: Record<string, number>
  totalScore: number
  interpretation: string
  isCompleted: boolean
}

interface ImagingStudyResult {
  testName: string
  findings: string
  impression: string
  recommendations: string
}

interface NeurologicalTestResult {
  testName: string
  findings: string
  interpretation: string
  recommendations: string
}

interface LaboratoryTestsData {
  selectedTests?: Record<string, boolean>
  bloodTestComponents?: Record<string, Record<string, BloodTestComponent>>
  bloodTestNotes?: Record<string, string>
  psychologicalAssessments?: Record<string, PsychologicalAssessmentResult>
  imagingStudies?: Record<string, ImagingStudyResult>
  neurologicalTests?: Record<string, NeurologicalTestResult>
}

interface LaboratoryTestsSectionProps {
  data: LaboratoryTestsData
  onUpdate: (data: LaboratoryTestsData) => void
}

export default function LaboratoryTestsSection({ data, onUpdate }: LaboratoryTestsSectionProps) {
  const [formData, setFormData] = useState<LaboratoryTestsData>(data || {
    selectedTests: {},
    bloodTestComponents: {},
    bloodTestNotes: {},
    psychologicalAssessments: {},
    imagingStudies: {},
    neurologicalTests: {}
  })

  useEffect(() => {
    onUpdate(formData)
  }, [formData, onUpdate])

  const handleTestSelection = (testName: string, category: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      selectedTests: {
        ...prev.selectedTests,
        [testName]: checked
      }
    }))
  }



  // Blood test component handlers
  const handleBloodTestComponentUpdate = (testName: string, componentName: string, field: keyof BloodTestComponent, value: string) => {
    setFormData(prev => ({
      ...prev,
      bloodTestComponents: {
        ...prev.bloodTestComponents,
        [testName]: {
          ...prev.bloodTestComponents?.[testName],
          [componentName]: {
            ...prev.bloodTestComponents?.[testName]?.[componentName],
            [field]: value
          } as BloodTestComponent
        }
      }
    }))
  }

  // Psychological assessment handlers
  const handlePsychologicalResponse = (testName: string, questionIndex: number, score: number) => {
    setFormData(prev => {
      const currentAssessment = prev.psychologicalAssessments?.[testName] || {
        testName,
        responses: {},
        totalScore: 0,
        interpretation: '',
        isCompleted: false
      }

      const updatedResponses = {
        ...currentAssessment.responses,
        [questionIndex.toString()]: score
      }

      const totalScore = Object.values(updatedResponses).reduce((sum, score) => sum + (score as number), 0)

      return {
        ...prev,
        psychologicalAssessments: {
          ...prev.psychologicalAssessments,
          [testName]: {
            ...currentAssessment,
            responses: updatedResponses,
            totalScore
          }
        }
      }
    })
  }

  const completePsychologicalAssessment = (testName: string) => {
    setFormData(prev => {
      const assessment = prev.psychologicalAssessments?.[testName]
      if (!assessment) return prev

      const questionnaire = PSYCHOLOGICAL_QUESTIONNAIRES[testName as keyof typeof PSYCHOLOGICAL_QUESTIONNAIRES]
      let interpretation = ''

      if (testName === 'PHQ-9 (Patient Health Questionnaire)') {
        if (assessment.totalScore <= 4) interpretation = 'Minimal depression'
        else if (assessment.totalScore <= 9) interpretation = 'Mild depression'
        else if (assessment.totalScore <= 14) interpretation = 'Moderate depression'
        else if (assessment.totalScore <= 19) interpretation = 'Moderately severe depression'
        else interpretation = 'Severe depression'
      } else if (testName === 'GAD-7 (Generalized Anxiety Disorder Scale)') {
        if (assessment.totalScore <= 4) interpretation = 'Minimal anxiety'
        else if (assessment.totalScore <= 9) interpretation = 'Mild anxiety'
        else if (assessment.totalScore <= 14) interpretation = 'Moderate anxiety'
        else interpretation = 'Severe anxiety'
      } else if (testName === 'MMSE (Mini-Mental State Exam)') {
        if (assessment.totalScore >= 24) interpretation = 'Normal cognition'
        else if (assessment.totalScore >= 18) interpretation = 'Mild cognitive impairment'
        else interpretation = 'Severe cognitive impairment'
      } else if (testName === 'DASS-21 (Depression, Anxiety and Stress Scale)') {
        // DASS-21 has subscales - calculate each subscale score
        const depressionItems = [2, 4, 9, 12, 15, 16, 20] // 0-indexed
        const anxietyItems = [1, 3, 6, 8, 14, 18, 19]
        const stressItems = [0, 5, 7, 10, 11, 13, 17]
        
        const depressionScore = depressionItems.reduce((sum, item) => sum + (assessment.responses?.[item.toString()] || 0), 0) * 2
        const anxietyScore = anxietyItems.reduce((sum, item) => sum + (assessment.responses?.[item.toString()] || 0), 0) * 2
        const stressScore = stressItems.reduce((sum, item) => sum + (assessment.responses?.[item.toString()] || 0), 0) * 2
        
        const getDepressionLevel = (score: number) => {
          if (score <= 9) return 'Normal'
          if (score <= 13) return 'Mild'
          if (score <= 20) return 'Moderate'
          if (score <= 27) return 'Severe'
          return 'Extremely Severe'
        }
        
        const getAnxietyLevel = (score: number) => {
          if (score <= 7) return 'Normal'
          if (score <= 9) return 'Mild'
          if (score <= 14) return 'Moderate'
          if (score <= 19) return 'Severe'
          return 'Extremely Severe'
        }
        
        const getStressLevel = (score: number) => {
          if (score <= 14) return 'Normal'
          if (score <= 18) return 'Mild'
          if (score <= 25) return 'Moderate'
          if (score <= 33) return 'Severe'
          return 'Extremely Severe'
        }
        
        interpretation = `Depression: ${depressionScore} (${getDepressionLevel(depressionScore)}), Anxiety: ${anxietyScore} (${getAnxietyLevel(anxietyScore)}), Stress: ${stressScore} (${getStressLevel(stressScore)})`
      }

      return {
        ...prev,
        psychologicalAssessments: {
          ...prev.psychologicalAssessments,
          [testName]: {
            ...assessment,
            interpretation,
            isCompleted: true
          }
        }
      }
    })
  }

  // Imaging study handlers
  const handleImagingStudyUpdate = (testName: string, field: keyof ImagingStudyResult, value: string) => {
    setFormData(prev => ({
      ...prev,
      imagingStudies: {
        ...prev.imagingStudies,
        [testName]: {
          ...prev.imagingStudies?.[testName],
          testName,
          [field]: value
        } as ImagingStudyResult
      }
    }))
  }

  // Neurological test handlers
  const handleNeurologicalTestUpdate = (testName: string, field: keyof NeurologicalTestResult, value: string) => {
    setFormData(prev => ({
      ...prev,
      neurologicalTests: {
        ...prev.neurologicalTests,
        [testName]: {
          ...prev.neurologicalTests?.[testName],
          testName,
          [field]: value
        } as NeurologicalTestResult
      }
    }))
  }

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-slate-900 mb-3">Laboratory and Assessment Tests</h2>
        <p className="text-base text-slate-600 leading-relaxed">Select tests performed and enter detailed results with clinical interpretations.</p>
      </div>

      {/* Enhanced Test Selection by Category */}
      {Object.entries(TEST_CATEGORIES).map(([category, tests]) => (
        <Card key={category} className="form-card-enhanced">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl font-semibold text-slate-800">{category}</CardTitle>
            <CardDescription className="text-slate-600">Select tests that have been performed for this patient</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {tests.map((testName) => {
                const isSelected = formData.selectedTests?.[testName] || false
                return (
                  <div key={testName} className="flex items-center space-x-3 p-2 rounded-md hover:bg-slate-50 transition-colors duration-200">
                    <Checkbox
                      id={`${category}-${testName}`}
                      checked={isSelected}
                      onCheckedChange={(checked) => handleTestSelection(testName, category, checked as boolean)}
                      className="symptom-checkbox"
                    />
                    <Label htmlFor={`${category}-${testName}`} className="symptom-label flex-1">
                      {testName}
                    </Label>
                  </div>
                )
              })}
            </div>

            {/* Conditional displays for all blood tests */}
            {category === "Blood Tests" && tests.some(test => formData.selectedTests?.[test] && BLOOD_TEST_COMPONENTS[test as keyof typeof BLOOD_TEST_COMPONENTS]) && (
              <div className="mt-6 space-y-4">
                {tests.filter(test => formData.selectedTests?.[test] && BLOOD_TEST_COMPONENTS[test as keyof typeof BLOOD_TEST_COMPONENTS]).map((testName) => {
                  const components = BLOOD_TEST_COMPONENTS[testName as keyof typeof BLOOD_TEST_COMPONENTS]

                  // Enhanced color coding for all blood test types
                  const getTestColors = (test: string) => {
                    switch (test) {
                      case "CBC (Complete Blood Count)":
                        return { bg: "bg-blue-50 border-blue-200", text: "text-blue-900", accent: "border-blue-300" }
                      case "Comprehensive Metabolic Panel":
                        return { bg: "bg-green-50 border-green-200", text: "text-green-900", accent: "border-green-300" }
                      case "Lipid Panel":
                        return { bg: "bg-purple-50 border-purple-200", text: "text-purple-900", accent: "border-purple-300" }
                      case "TSH (Thyroid Stimulating Hormone)":
                        return { bg: "bg-indigo-50 border-indigo-200", text: "text-indigo-900", accent: "border-indigo-300" }
                      case "T3 (Triiodothyronine)":
                        return { bg: "bg-cyan-50 border-cyan-200", text: "text-cyan-900", accent: "border-cyan-300" }
                      case "T4 (Thyroxine)":
                        return { bg: "bg-teal-50 border-teal-200", text: "text-teal-900", accent: "border-teal-300" }
                      case "Vitamin B12":
                        return { bg: "bg-amber-50 border-amber-200", text: "text-amber-900", accent: "border-amber-300" }
                      case "Vitamin D":
                        return { bg: "bg-yellow-50 border-yellow-200", text: "text-yellow-900", accent: "border-yellow-300" }
                      case "Folate":
                        return { bg: "bg-rose-50 border-rose-200", text: "text-rose-900", accent: "border-rose-300" }
                      default:
                        return { bg: "bg-orange-50 border-orange-200", text: "text-orange-900", accent: "border-orange-300" }
                    }
                  }

                  const colors = getTestColors(testName)
                  
                  return (
                    <div key={testName} className={`p-6 ${colors.bg} border-2 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200`}>
                      <h4 className={`text-xl font-bold ${colors.text} mb-6 flex items-center gap-2`}>
                        <div className={`w-3 h-3 rounded-full bg-current opacity-60`}></div>
                        {testName} - Components
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        {components.map((component) => (
                          <div key={component.name} className={`space-y-3 p-4 bg-white rounded-lg border ${colors.accent} shadow-sm`}>
                            <Label className={`form-label-enhanced font-semibold ${colors.text}`}>{component.label}</Label>
                            <Input
                              value={formData.bloodTestComponents?.[testName]?.[component.name]?.value || ''}
                              onChange={(e) => handleBloodTestComponentUpdate(testName, component.name, 'value', e.target.value)}
                              placeholder={`Enter value`}
                              className={`form-input-enhanced border-2 focus:border-current ${colors.text}`}
                            />
                            <div className="text-xs space-y-1">
                              <p className="text-slate-600 font-medium">Normal Range: {component.normalRange}</p>
                              <p className="text-slate-500">Unit: {component.unit}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                      <div className="mt-4 p-3 bg-white bg-opacity-50 rounded-lg">
                        <Label className={`form-label-enhanced ${colors.text} font-semibold`}>Additional Notes</Label>
                        <Textarea
                          value={formData.bloodTestNotes?.[testName] || ''}
                          onChange={(e) => {
                            setFormData(prev => ({
                              ...prev,
                              bloodTestNotes: {
                                ...prev.bloodTestNotes,
                                [testName]: e.target.value
                              }
                            }))
                          }}
                          placeholder="Add any additional notes or observations for this test..."
                          rows={2}
                          className="form-input-enhanced mt-2"
                        />
                      </div>
                    </div>
                  )
                })}
              </div>
            )}

            {category === "Imaging Studies" && tests.some(test => formData.selectedTests?.[test]) && (
              <div className="mt-6 space-y-4">
                {tests.filter(test => formData.selectedTests?.[test]).map((testName) => (
                  <div key={testName} className="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <h4 className="text-lg font-semibold text-green-900 mb-4">{testName} - Report</h4>
                    <div className="space-y-4">
                      <div>
                        <Label className="form-label-enhanced">Findings</Label>
                        <Textarea
                          value={formData.imagingStudies?.[testName]?.findings || ''}
                          onChange={(e) => handleImagingStudyUpdate(testName, 'findings', e.target.value)}
                          placeholder="Describe the imaging findings..."
                          rows={3}
                          className="form-input-enhanced"
                        />
                      </div>
                      <div>
                        <Label className="form-label-enhanced">Impression</Label>
                        <Textarea
                          value={formData.imagingStudies?.[testName]?.impression || ''}
                          onChange={(e) => handleImagingStudyUpdate(testName, 'impression', e.target.value)}
                          placeholder="Clinical impression based on findings..."
                          rows={2}
                          className="form-input-enhanced"
                        />
                      </div>
                      <div>
                        <Label className="form-label-enhanced">Recommendations</Label>
                        <Textarea
                          value={formData.imagingStudies?.[testName]?.recommendations || ''}
                          onChange={(e) => handleImagingStudyUpdate(testName, 'recommendations', e.target.value)}
                          placeholder="Clinical recommendations..."
                          rows={2}
                          className="form-input-enhanced"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {category === "Neurological Tests" && tests.some(test => formData.selectedTests?.[test]) && (
              <div className="mt-6 space-y-4">
                {tests.filter(test => formData.selectedTests?.[test]).map((testName) => (
                  <div key={testName} className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                    <h4 className="text-lg font-semibold text-purple-900 mb-4">{testName} - Results</h4>
                    <div className="space-y-4">
                      <div>
                        <Label className="form-label-enhanced">Test Findings</Label>
                        <Textarea
                          value={formData.neurologicalTests?.[testName]?.findings || ''}
                          onChange={(e) => handleNeurologicalTestUpdate(testName, 'findings', e.target.value)}
                          placeholder="Describe the neurological test findings..."
                          rows={3}
                          className="form-input-enhanced"
                        />
                      </div>
                      <div>
                        <Label className="form-label-enhanced">Clinical Interpretation</Label>
                        <Textarea
                          value={formData.neurologicalTests?.[testName]?.interpretation || ''}
                          onChange={(e) => handleNeurologicalTestUpdate(testName, 'interpretation', e.target.value)}
                          placeholder="Clinical interpretation of results..."
                          rows={2}
                          className="form-input-enhanced"
                        />
                      </div>
                      <div>
                        <Label className="form-label-enhanced">Recommendations</Label>
                        <Textarea
                          value={formData.neurologicalTests?.[testName]?.recommendations || ''}
                          onChange={(e) => handleNeurologicalTestUpdate(testName, 'recommendations', e.target.value)}
                          placeholder="Clinical recommendations based on results..."
                          rows={2}
                          className="form-input-enhanced"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      ))}

      {/* Psychological Assessment Questionnaires */}
      {Object.entries(PSYCHOLOGICAL_QUESTIONNAIRES).some(([testName]) => formData.selectedTests?.[testName]) && (
        <Card className="form-card-enhanced">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl font-semibold text-slate-800">Psychological Assessment Questionnaires</CardTitle>
            <CardDescription className="text-slate-600">Complete the selected psychological assessments</CardDescription>
          </CardHeader>
          <CardContent className="space-y-8">
            {Object.entries(PSYCHOLOGICAL_QUESTIONNAIRES).map(([testName, questionnaire]) => {
              if (!formData.selectedTests?.[testName]) return null

              const assessment = formData.psychologicalAssessments?.[testName]
              const isCompleted = assessment?.isCompleted || false

              return (
                <div key={testName} className="p-6 bg-amber-50 border border-amber-200 rounded-lg">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-lg font-semibold text-amber-900">{testName}</h4>
                    {isCompleted && assessment && (
                      <div className="flex items-center space-x-2">
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          Score: {assessment.totalScore}
                        </Badge>
                        <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                          {assessment.interpretation}
                        </Badge>
                      </div>
                    )}
                  </div>

                  {!isCompleted ? (
                    <div className="space-y-4">
                      <p className="text-sm text-amber-700 mb-4">
                        Rate each item: 0 = Not at all, 1 = Several days, 2 = More than half the days, 3 = Nearly every day
                      </p>
                      {questionnaire.questions.map((question, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-white rounded border">
                          <span className="text-sm font-medium text-slate-700 flex-1">{question}</span>
                          <div className="flex space-x-2 ml-4">
                            {[0, 1, 2, 3].map((score) => (
                              <label key={score} className="flex items-center space-x-1 cursor-pointer">
                                <input
                                  type="radio"
                                  name={`${testName}-${index}`}
                                  value={score}
                                  checked={assessment?.responses?.[index.toString()] === score}
                                  onChange={() => handlePsychologicalResponse(testName, index, score)}
                                  className="w-4 h-4 text-blue-600"
                                />
                                <span className="text-sm text-slate-600">{score}</span>
                              </label>
                            ))}
                          </div>
                        </div>
                      ))}
                      <div className="flex justify-between items-center mt-6">
                        <p className="text-sm text-slate-600">
                          Current Score: {assessment?.totalScore || 0}
                        </p>
                        <Button
                          onClick={() => completePsychologicalAssessment(testName)}
                          disabled={!assessment?.responses || Object.keys(assessment.responses).length < questionnaire.questions.length}
                          className="bg-amber-600 hover:bg-amber-700"
                        >
                          Finish Assessment
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="p-4 bg-white rounded border">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-lg font-semibold text-slate-800">
                            Final Score: {assessment?.totalScore || 0}
                          </p>
                          <p className="text-base text-slate-600">
                            Interpretation: {assessment?.interpretation || 'Not available'}
                          </p>
                        </div>
                        <Button
                          variant="outline"
                          onClick={() => {
                            setFormData(prev => ({
                              ...prev,
                              psychologicalAssessments: {
                                ...prev.psychologicalAssessments,
                                [testName]: {
                                  testName,
                                  responses: assessment?.responses || {},
                                  totalScore: assessment?.totalScore || 0,
                                  interpretation: assessment?.interpretation || '',
                                  isCompleted: false
                                }
                              }
                            }))
                          }}
                          className="text-amber-700 border-amber-300 hover:bg-amber-50"
                        >
                          Edit Responses
                        </Button>
                      </div>
                      <div className="mt-3 text-xs text-slate-500">
                        <p>Scoring Guide: {questionnaire.scoring}</p>
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </CardContent>
        </Card>
      )}

      {/* Summary message */}
      {Object.keys(formData.selectedTests || {}).filter(test => formData.selectedTests?.[test]).length === 0 && (
        <div className="text-center py-8 text-slate-500">
          <p>No tests selected.</p>
          <p className="text-sm">Select tests from the categories above to enter detailed results.</p>
        </div>
      )}
    </div>
  )
}

export type { LaboratoryTestsData }
