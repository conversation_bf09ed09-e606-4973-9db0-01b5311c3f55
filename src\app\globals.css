@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom styles for the psychiatric assessment app */
.assessment-container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
}

.form-section {
  @apply space-y-6 p-8 bg-white rounded-xl border border-slate-200 shadow-lg;
  transition: all 0.2s ease-in-out;
}

.form-section:hover {
  @apply shadow-xl border-slate-300;
  transform: translateY(-1px);
}

.progress-indicator {
  @apply flex items-center space-x-2 text-sm text-muted-foreground;
}

.autosave-indicator {
  @apply flex items-center space-x-2 text-sm text-emerald-600 bg-emerald-50 px-3 py-1.5 rounded-full border border-emerald-200;
  font-weight: 500;
}

.error-message {
  @apply text-sm text-red-600 mt-2 p-2 bg-red-50 border border-red-200 rounded-md;
}

.success-message {
  @apply text-sm text-emerald-600 mt-2 p-2 bg-emerald-50 border border-emerald-200 rounded-md;
}

/* Enhanced assessment form styling */
.assessment-header {
  @apply bg-white rounded-xl shadow-sm border border-slate-200 p-6 mb-8;
}

.assessment-progress-card {
  @apply bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200;
}

.assessment-tabs {
  @apply bg-white rounded-xl shadow-lg border border-slate-200 overflow-hidden;
}

.assessment-tab-trigger {
  @apply transition-all duration-200 hover:bg-slate-50;
}

.assessment-tab-trigger[data-state="active"] {
  @apply bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-md;
}

.assessment-tab-content {
  @apply p-8 bg-white;
}

/* Form input enhancements */
.form-input-enhanced {
  @apply transition-all duration-200 border-slate-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200;
}

.form-label-enhanced {
  @apply text-slate-700 font-medium text-sm mb-2;
}

.form-card-enhanced {
  @apply bg-white rounded-lg border border-slate-200 shadow-sm hover:shadow-md transition-all duration-200;
}

.symptom-category-card {
  @apply bg-gradient-to-br from-slate-50 to-slate-100 border-slate-200 hover:from-blue-50 hover:to-indigo-50 hover:border-blue-300 transition-all duration-300;
}

.symptom-checkbox {
  @apply w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500 focus:ring-2;
}

.symptom-label {
  @apply text-slate-700 text-sm font-medium cursor-pointer hover:text-blue-700 transition-colors duration-200;
}

/* Enhanced laboratory test styling */
.blood-test-card {
  @apply bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200 hover:from-blue-100 hover:to-blue-200 transition-all duration-300;
}

.imaging-test-card {
  @apply bg-gradient-to-br from-green-50 to-green-100 border-green-200 hover:from-green-100 hover:to-green-200 transition-all duration-300;
}

.psychological-test-card {
  @apply bg-gradient-to-br from-amber-50 to-amber-100 border-amber-200 hover:from-amber-100 hover:to-amber-200 transition-all duration-300;
}

.neurological-test-card {
  @apply bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200 hover:from-purple-100 hover:to-purple-200 transition-all duration-300;
}

/* Enhanced questionnaire styling */
.questionnaire-container {
  @apply bg-gradient-to-br from-amber-50 to-orange-50 border-amber-200 rounded-lg p-6 shadow-sm hover:shadow-md transition-all duration-300;
}

.questionnaire-question {
  @apply bg-white rounded-lg border border-slate-200 p-4 hover:border-slate-300 transition-all duration-200;
}

.questionnaire-response-option {
  @apply flex items-center space-x-2 cursor-pointer hover:bg-slate-50 p-2 rounded transition-colors duration-200;
}

.questionnaire-completed {
  @apply bg-gradient-to-br from-green-50 to-emerald-50 border-green-200;
}

/* Enhanced component input styling */
.component-input-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4;
}

.component-input-item {
  @apply space-y-2 p-3 bg-white rounded-lg border border-slate-200 hover:border-slate-300 transition-all duration-200;
}

.component-normal-range {
  @apply text-xs text-slate-500 font-medium bg-slate-50 px-2 py-1 rounded;
}

/* Enhanced progress and status indicators */
.progress-enhanced {
  @apply bg-gradient-to-r from-blue-200 to-indigo-200 h-3 rounded-full overflow-hidden;
}

.progress-bar-enhanced {
  @apply bg-gradient-to-r from-blue-600 to-indigo-600 h-full rounded-full transition-all duration-500 ease-out;
}

.status-badge-completed {
  @apply bg-gradient-to-r from-green-500 to-emerald-500 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-sm;
}

.status-badge-in-progress {
  @apply bg-gradient-to-r from-amber-500 to-orange-500 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-sm;
}

/* Enhanced button styling */
.btn-primary-enhanced {
  @apply bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold px-6 py-3 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 transform hover:-translate-y-0.5;
}

.btn-secondary-enhanced {
  @apply bg-gradient-to-r from-slate-100 to-slate-200 hover:from-slate-200 hover:to-slate-300 text-slate-700 font-semibold px-6 py-3 rounded-lg border border-slate-300 hover:border-slate-400 transition-all duration-200 transform hover:-translate-y-0.5;
}

/* Enhanced card animations */
.card-hover-lift {
  @apply transition-all duration-300 hover:shadow-xl hover:-translate-y-1;
}

.card-gradient-border {
  @apply bg-gradient-to-r from-blue-500 to-indigo-500 p-0.5 rounded-lg;
}

.card-gradient-content {
  @apply bg-white rounded-lg p-6;
}

/* Modern assessment page enhancements */
.assessment-page-container {
  @apply min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50;
}

.assessment-header-enhanced {
  @apply bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-8 mb-8;
  background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.9) 100%);
}

.assessment-title-enhanced {
  @apply text-4xl font-bold bg-gradient-to-r from-slate-900 via-blue-900 to-indigo-900 bg-clip-text text-transparent;
}

.assessment-subtitle-enhanced {
  @apply text-lg text-slate-600 font-medium;
}

.progress-card-enhanced {
  @apply bg-gradient-to-r from-blue-500/10 via-indigo-500/10 to-purple-500/10 border border-blue-200/50 rounded-xl p-6 backdrop-blur-sm;
}

.progress-bar-modern {
  @apply h-4 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full shadow-inner;
  background-size: 20px 20px;
  animation: progress-shimmer 2s linear infinite;
}

@keyframes progress-shimmer {
  0% { background-position: -20px 0; }
  100% { background-position: 20px 0; }
}

.section-tab-enhanced {
  @apply relative overflow-hidden transition-all duration-300 hover:scale-105;
}

.section-tab-enhanced::before {
  @apply absolute inset-0 bg-gradient-to-r from-blue-500/0 via-blue-500/5 to-blue-500/0 opacity-0 transition-opacity duration-300;
  content: '';
}

.section-tab-enhanced:hover::before {
  @apply opacity-100;
}

.section-tab-active {
  @apply bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg;
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.3);
}

.form-section-modern {
  @apply bg-white/90 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl p-8 space-y-8;
  background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,250,252,0.95) 100%);
}

.input-modern {
  @apply bg-white/80 border-slate-200 rounded-xl px-4 py-3 transition-all duration-200 focus:bg-white focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20;
}

.button-modern-primary {
  @apply bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold px-8 py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1;
}

.button-modern-secondary {
  @apply bg-white/80 hover:bg-white border border-slate-200 hover:border-slate-300 text-slate-700 font-semibold px-6 py-3 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 backdrop-blur-sm;
}

.card-modern {
  @apply bg-white/90 backdrop-blur-sm rounded-2xl border border-white/20 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1;
}

.badge-modern {
  @apply px-4 py-2 rounded-full text-sm font-semibold shadow-sm backdrop-blur-sm;
}

.badge-success {
  @apply bg-emerald-500/20 text-emerald-700 border border-emerald-200;
}

.badge-warning {
  @apply bg-amber-500/20 text-amber-700 border border-amber-200;
}

.badge-info {
  @apply bg-blue-500/20 text-blue-700 border border-blue-200;
}

/* Enhanced animations */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from { opacity: 0; transform: translateX(20px); }
  to { opacity: 1; transform: translateX(0); }
}

.pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite;
}

@keyframes pulseGlow {
  0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.3); }
  50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.6); }
}
